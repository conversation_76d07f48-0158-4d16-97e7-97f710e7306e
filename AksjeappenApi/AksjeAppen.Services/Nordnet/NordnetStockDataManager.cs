using AksjeAppen.Database;
using AksjeAppen.Mappers;
using AksjeAppen.Services.Nordnet.Data;
using Hangfire;
using Microsoft.Extensions.Logging;

namespace AksjeAppen.Services.Nordnet;

/// <summary>
    /// Manages the synchronization of stock data between Nordnet API and the local database.
    /// Handles fetching, comparing, and updating stock information efficiently.
    /// </summary>
    public class NordnetStockDataManager
    {
        private readonly NordetApiService _nordnetApiService;
        private readonly IStockRepository _stockRepository;
        private readonly ILogger<NordnetStockDataManager> _logger;

        public NordnetStockDataManager(
            NordetApiService nordnetApiService,
            IStockRepository stockRepository,
            ILogger<NordnetStockDataManager> logger)
        {
            _nordnetApiService = nordnetApiService;
            _stockRepository = stockRepository;
            _logger = logger;
        }
        
        /// <summary>
        /// Determines whether the stock update should be skipped based on current time and day.
        /// </summary>
        private bool ShouldSkipStockUpdate(out string reason)
        {
            var now = DateTime.Now;

            if (now.DayOfWeek == DayOfWeek.Saturday || now.DayOfWeek == DayOfWeek.Sunday)
            {
                reason = "Skipped due to weekend.";
                return true;
            }

            var startTime = new TimeSpan(8, 30, 0);
            var endTime = new TimeSpan(18, 0, 0);

            if (now.TimeOfDay < startTime || now.TimeOfDay > endTime)
            {
                reason = "Skipped due to time restriction.";
                return true;
            }

            reason = string.Empty;
            return false;
        }


        /// <summary>
        /// Updates the stock database with the latest data from Nordnet.
        /// Fetches all stocks, adds new ones, and updates existing ones.
        /// </summary>
        /// <returns>A summary of the update operation.</returns>
        [AutomaticRetry(Attempts = 0)]
        public async Task<StockUpdateSummary> UpdateStocksFromNordnet()
        {
            try
            {
                if (ShouldSkipStockUpdate(out var reason))
                {
                    _logger.LogInformation(reason);
                    return new StockUpdateSummary
                    {
                        Success = false,
                        TotalProcessed = 0,
                        Added = 0,
                        Updated = 0,
                        Failed = 0,
                        Message = reason
                    };
                }
                
                
                
                _logger.LogInformation("Starting stock data update from Nordnet");
                
                // Fetch all stocks from Nordnet API
                _logger.LogInformation("Fetching stock list from Nordnet API");
                var nordnetStocks = await _nordnetApiService.GetStockListAsync();
                _logger.LogInformation($"Retrieved {nordnetStocks.Count} stocks from Nordnet API");
                
                if (nordnetStocks == null || nordnetStocks.Count == 0)
                {
                    _logger.LogWarning("No stocks retrieved from Nordnet API");
                    return new StockUpdateSummary
                    {
                        Success = false,
                        Message = "No stocks retrieved from Nordnet API",
                        TotalProcessed = 0,
                        Added = 0,
                        Updated = 0,
                        Failed = 0
                    };
                }
                
                // Create a faux Root object to use with our mapper
                var nordnetRoot = new NordnetStockDataModel.Root
                {
                    Results = nordnetStocks,
                    Rows = nordnetStocks.Count,
                    TotalHits = nordnetStocks.Count
                };
                
                // Convert to our entity model for processing
                var stockEntities = StockMapper.MapFromNordnetData(nordnetRoot);
                
                // Get all instrument IDs from the fetched stocks
                var nordnetInstrumentIds = stockEntities.Select(s => s.InstrumentId).ToHashSet();
                
                // We'll process stocks in batches for better performance and memory efficiency
                var batchSize = 200;
                var processingBatches = stockEntities
                    .Select((stock, index) => new { stock, index })
                    .GroupBy(x => x.index / batchSize)
                    .Select(g => g.Select(x => x.stock).ToList())
                    .ToList();
                
                var updateSummary = new StockUpdateSummary
                {
                    Success = true,
                    TotalProcessed = stockEntities.Count,
                    Added = 0,
                    Updated = 0,
                    Failed = 0
                };
                
                // Process each batch
                foreach (var batch in processingBatches)
                {
                    try
                    {
                        // Check which stocks already exist in the database
                        var batchInstrumentIds = batch.Select(s => s.InstrumentId).ToList();
                        var existingStocks = await _stockRepository.GetStocksByInstrumentIdsAsync(batchInstrumentIds);
                        var existingInstrumentIds = existingStocks.Select(s => s.InstrumentId).ToHashSet();
                        
                        // Separate into new and existing stocks
                        var newStocks = batch.Where(s => !existingInstrumentIds.Contains(s.InstrumentId)).ToList();
                        var stocksToUpdate = batch.Where(s => existingInstrumentIds.Contains(s.InstrumentId)).ToList();
                        
                        // Process new stocks
                        if (newStocks.Any())
                        {
                            _logger.LogInformation($"Adding {newStocks.Count} new stocks to the database");
                            await _stockRepository.AddOrUpdateStocksAsync(newStocks);
                            updateSummary.Added += newStocks.Count;
                        }
                        
                        // Process existing stocks that need updates
                        if (stocksToUpdate.Any())
                        {
                            _logger.LogInformation($"Updating {stocksToUpdate.Count} existing stocks");
                            await _stockRepository.AddOrUpdateStocksAsync(stocksToUpdate);
                            updateSummary.Updated += stocksToUpdate.Count;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Error processing batch of {batch.Count} stocks");
                        updateSummary.Failed += batch.Count;
                    }
                }
                
                // Check for stocks in the database that are no longer in Nordnet data
                // This is optional and depends on whether you want to keep or remove such stocks
                /*
                var allDatabaseStocks = await _stockRepository.GetAllInstrumentIdsAsync();
                var stocksToDeactivate = allDatabaseStocks.Where(id => !nordnetInstrumentIds.Contains(id)).ToList();
                
                if (stocksToDeactivate.Any())
                {
                    _logger.LogInformation($"Deactivating {stocksToDeactivate.Count} stocks no longer present in Nordnet data");
                    await _stockRepository.DeactivateStocksByInstrumentIdsAsync(stocksToDeactivate);
                    updateSummary.Deactivated = stocksToDeactivate.Count;
                }
                */
                
                _logger.LogInformation($"Stock update completed. Added: {updateSummary.Added}, Updated: {updateSummary.Updated}, Failed: {updateSummary.Failed}");
                updateSummary.Message = "Stock update completed successfully";
                return updateSummary;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to update stocks from Nordnet");
                return new StockUpdateSummary
                {
                    Success = false,
                    Message = $"Failed to update stocks: {ex.Message}",
                    TotalProcessed = 0,
                    Added = 0,
                    Updated = 0,
                    Failed = 0
                };
            }
        }
    }

    /// <summary>
    /// Represents a summary of a stock update operation.
    /// </summary>
    public class StockUpdateSummary
    {
        /// <summary>
        /// Whether the update operation was successful.
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// A message describing the result of the update operation.
        /// </summary>
        public string Message { get; set; }
        
        /// <summary>
        /// The total number of stocks processed.
        /// </summary>
        public int TotalProcessed { get; set; }
        
        /// <summary>
        /// The number of new stocks added to the database.
        /// </summary>
        public int Added { get; set; }
        
        /// <summary>
        /// The number of existing stocks updated in the database.
        /// </summary>
        public int Updated { get; set; }
        
        /// <summary>
        /// The number of stocks that failed to process.
        /// </summary>
        public int Failed { get; set; }
        
        /// <summary>
        /// The number of stocks that were deactivated (if tracking removed stocks).
        /// </summary>
        public int Deactivated { get; set; }
    }