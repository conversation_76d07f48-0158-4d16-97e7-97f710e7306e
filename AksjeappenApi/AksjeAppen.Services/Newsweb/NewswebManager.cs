

using System.Data;
using System.Text.Json;
using AksjeAppen.Services.Newsweb;
using Dapper;
using FinanceApi.Newsweb.Dto;
using Hangfire;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Npgsql;
using NpgsqlTypes;

namespace FinanceApi.NewswebService.Core;

public class NewsWebManager
{
    private readonly string _connectionString;
    private NpgsqlConnection _connection;
    private readonly NewswebEventJobService _newswebEventJobService;
    private readonly ILogger<NewsWebManager> _logger;
    
    public NewsWebManager(ILogger<NewsWebManager> logger, string connectionString, NewswebEventJobService newswebEventJobService)
    {
        _connectionString = connectionString;
        _connection = new NpgsqlConnection(_connectionString);
        _newswebEventJobService = newswebEventJobService;
        _logger = logger;
    }
    
    /// <summary>
    /// Determines whether the stock update should be skipped based on current time and day.
    /// </summary>
    private bool ShouldUpdate(out string reason)
    {
#if !DEBUG
        var now = DateTime.Now;

        if (now.DayOfWeek == DayOfWeek.Saturday || now.DayOfWeek == DayOfWeek.Sunday)
        {
            reason = "Skipped due to weekend.";
            return true;
        }

        var startTime = new TimeSpan(6, 30, 0);
        var endTime = new TimeSpan(20, 0, 0);

        if (now.TimeOfDay < startTime || now.TimeOfDay > endTime)
        {
            reason = "Skipped due to time restriction.";
            return true;
        }
#endif

        reason = string.Empty;
        return false;
    }
    
    [AutomaticRetry(Attempts = 0)]
    public async Task<List<NewsWebDto>> FetchAndUpdateMessagesFromNewsweb(int maxMessages = 20, int timeout = 60) // it happened that newsweb published 5 "renteendringer" in a row, 
    {
        
        if (ShouldUpdate(out var reason))
        {
            _logger.LogInformation("Skipped fetching and updating messages from Newsweb. + {reason}");
            // _logger.LogInformation(reason);
        //     return new StockUpdateSummary
        //     {
        //         Success = false,
        //         TotalProcessed = 0,
        //         Added = 0,
        //         Updated = 0,
        //         Failed = 0,
        //         Message = reason
        //     };
            return new List<NewsWebDto>();
        }
        
        using (var cts = new CancellationTokenSource())
        {
            var newsWebMessagesAdded = new List<NewsWebDto>();
            var newsWebMessages = await NewswebFetchService.GetStockListAsync(cts.Token, maxMessages);
            var messageIds = newsWebMessages.Select(m => m.MessageId).ToList();
            var existingMessageIds = await GetExistingMessageIds(messageIds);
            var newMessages = newsWebMessages
                .Where(message => !existingMessageIds.Contains(message.MessageId))
                .ToList();
            
            foreach (var message in newMessages)
            {
                try
                {
                    // Fetch detailed message information
                    var detailedMessage = await NewswebFetchService.GetMessageDetailsAsync(message.MessageId.ToString(), cts.Token);
                        
                    // Save message to database
                    var addedMessage = await SaveToDb(detailedMessage);
                        
                    if (addedMessage != null)
                    {
                        newsWebMessagesAdded.Add(addedMessage);
                        _newswebEventJobService.ScheduleNewswebEventJob(message);
                        // Schedule notifications
                        // _newswebScheduleService.ScheduleNotificaitonToSubscribedNewsWebUsers(new NewsWebDto(addedMessage));
                    }
                }
                catch (Exception ex)
                {
                    //Log.Error(ex, $"Error processing message with ID {message.MessageId}");
                }
            }
            
            return newsWebMessagesAdded;
            
            
        }

        return new List<NewsWebDto>();
    }
    private async Task EnsureConnectionOpenAsync()
    {
        if (_connection.State != System.Data.ConnectionState.Open)
        {
            await _connection.OpenAsync();
        }
    }
    
    private async Task<List<int>> GetExistingMessageIds(List<int> messageIds)
    {
        try
        {
            await EnsureConnectionOpenAsync();

            var query = @"
            SELECT ""message_id"" 
            FROM newsweb_messages 
            WHERE ""message_id"" = ANY(@MessageIds)";

            var parameters = new { MessageIds = messageIds };

            var res = (await _connection.QueryAsync<int>(query, parameters)).ToList();
            return res;
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
        finally
        {
            if (_connection?.State == System.Data.ConnectionState.Open)
            {
                await _connection.CloseAsync();
            }
        }
    }
    
    public class JObjectHandler : SqlMapper.TypeHandler<JObject>
    {
        public override JObject Parse(object value)
        {
            return value == null ? null : JObject.Parse(value.ToString());
        }

        public override void SetValue(IDbDataParameter parameter, JObject value)
        {
            parameter.Value = value?.ToString(Newtonsoft.Json.Formatting.None) ?? (object)DBNull.Value;
            if (parameter is NpgsqlParameter npgsqlParameter)
            {
                npgsqlParameter.NpgsqlDbType = NpgsqlDbType.Jsonb;
            }
        }
    }
    
    // Option 1: Update your JsonTypeHandler to use Newtonsoft.Json instead of System.Text.Json
    public class JsonTypeHandler<T> : SqlMapper.TypeHandler<T>
    {
        public override void SetValue(IDbDataParameter parameter, T value)
        {
            parameter.Value = JsonConvert.SerializeObject(value);
            if (parameter is NpgsqlParameter npgsqlParameter)
            {
                npgsqlParameter.NpgsqlDbType = NpgsqlDbType.Jsonb;
            }
        }

        public override T Parse(object value)
        {
            if (value == null || value is DBNull)
                return default;
            
            return JsonConvert.DeserializeObject<T>(value.ToString());
        }
    }
    
    public class JsonDocumentHandler : SqlMapper.TypeHandler<JsonDocument>
    {
        public override JsonDocument Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;

            return JsonDocument.Parse(value.ToString());
        }

        public override void SetValue(IDbDataParameter parameter, JsonDocument value)
        {
            if (value == null)
            {
                parameter.Value = DBNull.Value;
            }
            else
            {
                parameter.Value = value.RootElement.GetRawText();
            }

            if (parameter is NpgsqlParameter npgsqlParameter)
            {
                npgsqlParameter.NpgsqlDbType = NpgsqlDbType.Jsonb;
            }
        }
    }
    
    // Option 2: Create a specialized handler for List<Attachment>
    public class AttachmentListHandler : SqlMapper.TypeHandler<List<Attachment>>
    {
        public override List<Attachment> Parse(object value)
        {
            if (value == null || value is DBNull)
                return null;
            
            return JsonConvert.DeserializeObject<List<Attachment>>(value.ToString());
        }

        public override void SetValue(IDbDataParameter parameter, List<Attachment> value)
        {
            if (value == null)
            {
                parameter.Value = DBNull.Value;
            }
            else
            {
                parameter.Value = JsonConvert.SerializeObject(value);
            }

            if (parameter is NpgsqlParameter npgsqlParameter)
            {
                npgsqlParameter.NpgsqlDbType = NpgsqlDbType.Jsonb;
            }
        }
    }
    
private async Task<NewsWebDto> SaveToDb(Message message)
{
    SqlMapper.AddTypeHandler(new JObjectHandler());
    SqlMapper.AddTypeHandler(new JsonTypeHandler<List<string>>());
    SqlMapper.AddTypeHandler(new JsonTypeHandler<List<Attachment>>());
    SqlMapper.AddTypeHandler(new JsonTypeHandler<List<Category>>());

    try
    {
        await EnsureConnectionOpenAsync();
        await using var transaction = await _connection.BeginTransactionAsync();

        try
        {
            var insertQuery = @"
                INSERT INTO newsweb_messages (
                    id,
                    message_id, 
                    news_id, 
                    title, 
                    body, 
                    issuer_id, 
                    issuer_sign, 
                    issuer_name, 
                    instr_id, 
                    instrument_name, 
                    instrument_full_name,
                    correction_for_message_id, 
                    corrected_by_message_id, 
                    published_time, 
                    numb_attachments,
                    info_required, 
                    oam_mandatory,
                    attachments, 
                    categories, 
                    markets,
                    processed_date_time,
                    processed_timestamp
                ) 
                VALUES (
                    @Id,
                    @MessageId, 
                    @NewsId, 
                    @Title, 
                    @Body, 
                    @IssuerId, 
                    @IssuerSign, 
                    @IssuerName, 
                    @InstrId, 
                    @InstrumentName, 
                    @InstrumentFullName,
                    @CorrectionForMessageId, 
                    @CorrectedByMessageId, 
                    @PublishedTime, 
                    @NumbAttachments, 
                    @InfoRequired, 
                    @OamMandatory, 
                    @Attachments::jsonb, 
                    @Categories::jsonb, 
                    @Markets::jsonb,
                    @ProcessedDateTime,
                    @ProcessedTimestamp
                )
                RETURNING *";

            var parameters = new
            {
                message.Id,
                message.MessageId,
                message.NewsId,
                message.Title,
                message.Body,
                message.IssuerId,
                message.IssuerSign,
                message.IssuerName,
                message.InstrId,
                message.InstrumentName,
                message.InstrumentFullName,
                message.CorrectionForMessageId,
                message.CorrectedByMessageId,
                PublishedTime = DateTime.SpecifyKind(message.PublishedTime, DateTimeKind.Utc),
                message.NumbAttachments,
                InfoRequired = message.InfoRequired != null ? (int)message.InfoRequired : 0,
                OamMandatory = message.OamMandatory != null ? (int)message.OamMandatory : 0,
                Attachments = (message.Attachments != null && message.Attachments.Count > 0) ? message.Attachments : null,
                Categories = message.Category ?? new List<Category>(),
                Markets = message.Markets ?? new List<string>(),
                ProcessedDateTime = DateTime.UtcNow,
                ProcessedTimestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()
            };

            var newWebMessage = await _connection.QuerySingleAsync<NewsWebDto>(insertQuery, parameters, transaction);
            await transaction.CommitAsync();
            return newWebMessage;
        }
        catch (Exception ex)
        {
            await transaction.RollbackAsync();
            // Log error if needed
            return null;
        }
    }
    catch (Exception ex)
    {
        // Log error if needed
        return null;
    }
    finally
    {
        if (_connection?.State == System.Data.ConnectionState.Open)
        {
            await _connection.CloseAsync();
        }
    }
}
    
}

